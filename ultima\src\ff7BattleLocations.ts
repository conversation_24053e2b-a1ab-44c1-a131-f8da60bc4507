export const BattleLocations = [
    "Blank",
    "Bizarro Battle - Center",
    "Grassland",
    "Mt Nibel",
    "Forest",
    "Beach",
    "Desert",
    "Snow",
    "Swamp",
    "Sector 1 Train Station",
    "Reactor 1",
    "Reactor 1 Core",
    "Reactor 1 Entrance",
    "Sector 4 Subway",
    "Nibel Caves",
    "Shinra HQ",
    "Midgar Raid Subway",
    "Hojo's Lab",
    "Shinra Elevators",
    "Shinra Roof",
    "Midgar Highway",
    "Wutai Pagoda",
    "Church",
    "Coral Valley",
    "Midgar Slums",
    "Sector 4 Corridors or Junon Path",
    "Sector 4 Gantries or Midgar Underground",
    "Sector 7 Support Pillar Stairway",
    "Sector 7 Support Pillar Top",
    "Sector 8",
    "Sewers",
    "Mythril Mines",
    "Northern Crater - Floating Platforms",
    "Corel Mountain Path",
    "Junon Beach",
    "Junon Cargo Ship",
    "Corel Prison",
    "Battle Square",
    "Da Chao - Rapps Battle",
    "Cid's Backyard",
    "Final Descent to Sephiroth",
    "Reactor 5 Entrance",
    "Temple of the Ancients - Escher Room",
    "Shinra Mansion",
    "Junon Airship Dock",
    "Whirlwind Maze",
    "Junon Underwater Reactor",
    "Gongaga Reactor",
    "Gelnika",
    "Train Graveyard",
    "Great Glacier Ice Caves & Gaea Cliffs - Inside",
    "Sister Ray",
    "Sister Ray Base",
    "Forgotten City Altar",
    "Northern Crater - Initial Descent",
    "Northern Crater - Hatchery",
    "Northern Crater - Water Area",
    "Safer Battle",
    "Kalm Flashback - Dragon Battle",
    "Junon Underwater Pipe",
    "Blank",
    "Corel Railway - Canyon",
    "Whirlwind Maze - Crater",
    "Corel Railway - Rollercoaster",
    "Wooden Bridge",
    "Da Chao",
    "Fort Condor",
    "Dirt Wasteland",
    "Bizarro Battle - Right Side",
    "Bizarro Battle - Left Side",
    "Jenova*SYNTHESIS Battle",
    "Corel Train Battle",
    "Cosmo Canyon",
    "Caverns of the Gi",
    "Nibelheim Mansion Basement",
    "Temple of the Ancients - Demons Gate",
    "Temple of the Ancients - Mural Room",
    "Temple of the Ancients - Clock Passage",
    "Final Battle - Sephiroth",
    "Jungle",
    "Ultimate Weapon - Battle on Highwind",
    "Corel Reactor",
    "Unused",
    "Don Corneo's Mansion",
    "Emerald Weapon Battle",
    "Reactor 5",
    "Shinra HQ - Escape",
    "Ultimate Weapon - Gongaga Reactor",
    "Corel Prison - Dyne Battle",
    "Ultimate Weapon - Forest"
] as const;

export type BattleLocation = typeof BattleLocations[number];

