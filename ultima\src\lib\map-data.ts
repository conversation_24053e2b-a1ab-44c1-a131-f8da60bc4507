import { WorldMapTexture } from "@/ff7/texfile";

export const REGION_NAMES: Record<number, string> = {
  0: 'Midgar Area',
  1: 'Grasslands Area',
  2: 'Junon Area',
  3: 'Corel Area',
  4: 'Gold Saucer Area',
  5: 'Gongaga Area',
  6: 'Cosmo Area',
  7: 'Nibel Area',
  8: 'Rocket Launch Pad Area',
  9: 'Wutai Area',
  10: 'Woodlands Area',
  11: 'Icicle Area',
  12: 'Mideel Area',
  13: 'North Corel Area',
  14: 'Cactus Island',
  15: 'Goblin Island',
  16: 'Round Island',
  17: 'Sea',
  18: 'Bottom of the Sea',
  19: 'Glacier'
};

export const TRIANGLE_TYPES: Record<number, { type: string; description: string }> = {
  0: { type: 'Grass', description: 'Most things can go here.' },
  1: { type: 'Forest', description: 'No landing here, but anything else goes.' },
  2: { type: 'Mountain', description: 'Chocobos and flying machines only.' },
  3: { type: 'Sea', description: 'Deep water, only gold chocobo and submarine can go here.' },
  4: { type: 'River Crossing', description: 'Buggy, tiny bronco and water-capable chocobos.' },
  5: { type: 'River', description: 'Tiny bronco and chocobos.' },
  6: { type: 'Water', description: 'Shallow water, same as above.' },
  7: { type: 'Swamp', description: 'Midgar zolom can only move in swamp areas.' },
  8: { type: 'Desert', description: 'No landing.' },
  9: { type: 'Wasteland', description: 'Found around Midgar, Wutai and misc other. No landing.' },
  10: { type: 'Snow', description: 'Leaves footprints, no landing.' },
  11: { type: 'Riverside', description: 'Beach-like area where river and land meet.' },
  12: { type: 'Cliff', description: 'Sharp drop, usually where the player can be on either side.' },
  13: { type: 'Corel Bridge', description: 'Tiny bridge over the waterfall from Costa del Sol to Corel.' },
  14: { type: 'Wutai Bridge', description: 'Rickety rope bridges south of Wutai.' },
  15: { type: 'Unused', description: "Doesn't seem to be used anywhere in the original data." },
  16: { type: 'Hill side', description: 'This is the tiny walkable part at the foot of a mountain.' },
  17: { type: 'Beach', description: 'Where land and shallow water meets.' },
  18: { type: 'Sub Pen', description: 'Only place where you can enter/exit the submarine.' },
  19: { type: 'Canyon', description: 'The ground in cosmo canyon has this type, walkability seems to be the same as wasteland.' },
  20: { type: 'Mountain Pass', description: 'The small path through the mountains connecting Costa del Sol and Corel.' },
  21: { type: 'Unknown (21)', description: 'Present around bridges and the Northern Crater, may have some special meaning.' },
  22: { type: 'Waterfall', description: "River type where the tiny bronco can't go." },
  23: { type: 'Unused', description: "Doesn't seem to be used anywhere in the original data." },
  24: { type: 'Gold Saucer Desert', description: 'Special desert type for the golden saucer.' },
  25: { type: 'Jungle', description: 'Walkability same as forest, used in southern parts of the map.' },
  26: { type: 'Sea (2)', description: 'Special type of deep water, only used in one small spot next to HP-MP cave, possibly related to the underwater map/submarine.' },
  27: { type: 'Northern Cave', description: 'Inside part of the crater, where you can land the highwind.' },
  28: { type: 'Gold Saucer Desert Border', description: 'Narrow strip of land surrounding the golden saucer desert. Probably related to the "quicksand" script.' },
  29: { type: 'Bridgehead', description: 'Small area at both ends of every bridge. May have some special meaning.' },
  30: { type: 'Back Entrance', description: 'Special type that can be set unwalkable from the script.' },
  31: { type: 'Unused', description: "Doesn't seem to be used anywhere in the original data." }
}; 

export const WORLD_MAP_OVERWORLD_TEXTURES: WorldMapTexture[] = [
  { id: 0, name: "pond", width: 32, height: 32, uOffset: 0, vOffset: 352 },
  { id: 1, name: "riv_m2", width: 32, height: 32, uOffset: 128, vOffset: 64 },
  { id: 2, name: "was_gs", width: 64, height: 64, uOffset: 64, vOffset: 192 },
  { id: 3, name: "wpcltr", width: 32, height: 128, uOffset: 0, vOffset: 256 },
  { id: 4, name: "wpcltr2", width: 32, height: 32, uOffset: 160, vOffset: 64 },
  { id: 5, name: "bzdun", width: 64, height: 64, uOffset: 192, vOffset: 192 },
  { id: 6, name: "bone", width: 32, height: 32, uOffset: 224, vOffset: 384 },
  { id: 7, name: "bone2", width: 32, height: 32, uOffset: 224, vOffset: 416 },
  { id: 8, name: "bornwd", width: 64, height: 64, uOffset: 160, vOffset: 320 },
  { id: 9, name: "bridge", width: 32, height: 64, uOffset: 192, vOffset: 0 },
  { id: 10, name: "bridge2", width: 32, height: 32, uOffset: 224, vOffset: 0 },
  { id: 11, name: "cave", width: 32, height: 32, uOffset: 224, vOffset: 448 },
  { id: 12, name: "cave2", width: 32, height: 32, uOffset: 224, vOffset: 320 },
  { id: 13, name: "cave_s", width: 32, height: 32, uOffset: 160, vOffset: 224 },
  { id: 14, name: "cdl_cl2", width: 64, height: 32, uOffset: 96, vOffset: 96 },
  { id: 15, name: "cf01", width: 64, height: 32, uOffset: 192, vOffset: 288 },
  { id: 16, name: "clf_bgs", width: 64, height: 32, uOffset: 192, vOffset: 384 },
  { id: 17, name: "clf_ggl", width: 64, height: 64, uOffset: 128, vOffset: 256 },
  { id: 18, name: "clf_ggs", width: 64, height: 32, uOffset: 192, vOffset: 352 },
  { id: 19, name: "clf_l", width: 64, height: 64, uOffset: 0, vOffset: 0 },
  { id: 20, name: "clf_ld", width: 64, height: 64, uOffset: 64, vOffset: 0 },
  { id: 21, name: "clf_lf", width: 64, height: 64, uOffset: 128, vOffset: 0 },
  { id: 22, name: "clf_lg", width: 32, height: 64, uOffset: 0, vOffset: 96 },
  { id: 23, name: "clf_lr", width: 32, height: 64, uOffset: 128, vOffset: 0 },
  { id: 24, name: "clf_lsg", width: 32, height: 64, uOffset: 64, vOffset: 64 },
  { id: 25, name: "clf_r", width: 32, height: 32, uOffset: 0, vOffset: 96 },
  { id: 26, name: "clf_s", width: 64, height: 32, uOffset: 192, vOffset: 0 },
  { id: 27, name: "clf_sd", width: 64, height: 32, uOffset: 192, vOffset: 32 },
  { id: 28, name: "clf_sf", width: 64, height: 32, uOffset: 0, vOffset: 64 },
  { id: 29, name: "clf_sg", width: 32, height: 32, uOffset: 32, vOffset: 96 },
  { id: 30, name: "clf_sg2", width: 32, height: 32, uOffset: 0, vOffset: 160 },
  { id: 31, name: "clf_sr", width: 32, height: 32, uOffset: 32, vOffset: 96 },
  { id: 32, name: "clf_ss", width: 32, height: 32, uOffset: 32, vOffset: 128 },
  { id: 33, name: "clf_ssd", width: 32, height: 32, uOffset: 0, vOffset: 224 },
  { id: 34, name: "clf_ssw", width: 32, height: 32, uOffset: 224, vOffset: 32 },
  { id: 35, name: "clf_sw", width: 32, height: 32, uOffset: 192, vOffset: 32 },
  { id: 36, name: "clf_w02", width: 64, height: 64, uOffset: 128, vOffset: 64 },
  { id: 37, name: "clf_w03", width: 64, height: 64, uOffset: 192, vOffset: 64 },
  { id: 38, name: "clf_was", width: 64, height: 32, uOffset: 64, vOffset: 64 },
  { id: 39, name: "clfeg", width: 32, height: 32, uOffset: 192, vOffset: 320 },
  { id: 40, name: "clfegd", width: 32, height: 32, uOffset: 0, vOffset: 320 },
  { id: 41, name: "clftop", width: 64, height: 32, uOffset: 192, vOffset: 64 },
  { id: 42, name: "clftop2", width: 32, height: 32, uOffset: 128, vOffset: 64 },
  { id: 43, name: "cndl_cl", width: 64, height: 32, uOffset: 96, vOffset: 128 },
  { id: 44, name: "cndlf", width: 64, height: 64, uOffset: 160, vOffset: 64 },
  { id: 45, name: "cndlf02", width: 64, height: 64, uOffset: 208, vOffset: 64 },
  { id: 46, name: "comtr", width: 16, height: 32, uOffset: 144, vOffset: 96 },
  { id: 47, name: "cosinn", width: 32, height: 32, uOffset: 224, vOffset: 416 },
  { id: 48, name: "cosinn2", width: 32, height: 32, uOffset: 192, vOffset: 448 },
  { id: 49, name: "csmk", width: 32, height: 32, uOffset: 64, vOffset: 64 },
  { id: 50, name: "csmk2", width: 32, height: 32, uOffset: 96, vOffset: 64 },
  { id: 51, name: "cstds01", width: 32, height: 32, uOffset: 224, vOffset: 160 },
  { id: 52, name: "cstds02", width: 64, height: 64, uOffset: 0, vOffset: 448 },
  { id: 53, name: "des01", width: 32, height: 32, uOffset: 160, vOffset: 320 },
  { id: 54, name: "desert", width: 64, height: 64, uOffset: 128, vOffset: 128 },
  { id: 55, name: "desor", width: 64, height: 32, uOffset: 160, vOffset: 64 },
  { id: 56, name: "ds1", width: 32, height: 32, uOffset: 0, vOffset: 256 },
  { id: 57, name: "ds_s1", width: 32, height: 32, uOffset: 192, vOffset: 288 },
  { id: 58, name: "dsee1", width: 32, height: 32, uOffset: 96, vOffset: 288 },
  { id: 59, name: "dsrt_d", width: 32, height: 32, uOffset: 224, vOffset: 0 },
  { id: 60, name: "dsrt_e", width: 64, height: 128, uOffset: 64, vOffset: 128 },
  { id: 61, name: "edes01", width: 32, height: 32, uOffset: 224, vOffset: 320 },
  { id: 62, name: "elm01", width: 32, height: 32, uOffset: 160, vOffset: 0 },
  { id: 63, name: "elm02", width: 32, height: 32, uOffset: 64, vOffset: 96 },
  { id: 64, name: "elm_gro", width: 64, height: 64, uOffset: 0, vOffset: 96 },
  { id: 65, name: "elm_r", width: 32, height: 32, uOffset: 192, vOffset: 0 },
  { id: 66, name: "elm_r2", width: 32, height: 32, uOffset: 224, vOffset: 0 },
  { id: 67, name: "fall1", width: 32, height: 32, uOffset: 128, vOffset: 256 },
  { id: 68, name: "farm01", width: 32, height: 32, uOffset: 160, vOffset: 32 },
  { id: 69, name: "farm02", width: 32, height: 32, uOffset: 192, vOffset: 32 },
  { id: 70, name: "farm_g", width: 32, height: 32, uOffset: 128, vOffset: 64 },
  { id: 71, name: "farm_r", width: 32, height: 16, uOffset: 128, vOffset: 48 },
  { id: 72, name: "fld", width: 64, height: 64, uOffset: 64, vOffset: 96 },
  { id: 73, name: "fld_02", width: 64, height: 64, uOffset: 0, vOffset: 64 },
  { id: 74, name: "fld_s", width: 64, height: 64, uOffset: 0, vOffset: 160 },
  { id: 75, name: "fld_s2", width: 32, height: 32, uOffset: 224, vOffset: 256 },
  { id: 76, name: "fld_sw", width: 64, height: 64, uOffset: 128, vOffset: 192 },
  { id: 77, name: "fld_v", width: 128, height: 128, uOffset: 0, vOffset: 128 },
  { id: 78, name: "fld_vd", width: 32, height: 64, uOffset: 96, vOffset: 128 },
  { id: 79, name: "fld_vd2", width: 32, height: 64, uOffset: 192, vOffset: 416 },
  { id: 80, name: "fvedge", width: 32, height: 64, uOffset: 0, vOffset: 0 },
  { id: 81, name: "gclf_d", width: 128, height: 64, uOffset: 128, vOffset: 128 },
  { id: 82, name: "gclf_g", width: 32, height: 64, uOffset: 224, vOffset: 128 },
  { id: 83, name: "gclfwa", width: 128, height: 64, uOffset: 64, vOffset: 320 },
  { id: 84, name: "gclfwa2", width: 32, height: 64, uOffset: 160, vOffset: 320 },
  { id: 85, name: "gclfwag", width: 32, height: 64, uOffset: 32, vOffset: 320 },
  { id: 86, name: "gg_gro", width: 64, height: 64, uOffset: 64, vOffset: 448 },
  { id: 87, name: "gg_mts", width: 64, height: 128, uOffset: 0, vOffset: 128 },
  { id: 88, name: "ggmk", width: 64, height: 64, uOffset: 128, vOffset: 448 },
  { id: 89, name: "ggmt", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 90, name: "ggmt_e", width: 128, height: 32, uOffset: 0, vOffset: 96 },
  { id: 91, name: "ggmt_ed", width: 128, height: 32, uOffset: 128, vOffset: 96 },
  { id: 92, name: "ggmt_eg", width: 32, height: 32, uOffset: 96, vOffset: 224 },
  { id: 93, name: "ggmtd", width: 128, height: 128, uOffset: 128, vOffset: 0 },
  { id: 94, name: "ggs_g", width: 32, height: 32, uOffset: 32, vOffset: 64 },
  { id: 95, name: "ggshr", width: 32, height: 32, uOffset: 192, vOffset: 96 },
  { id: 96, name: "ggshrg", width: 32, height: 32, uOffset: 224, vOffset: 96 },
  { id: 97, name: "gia", width: 64, height: 32, uOffset: 64, vOffset: 224 },
  { id: 98, name: "gia2", width: 64, height: 32, uOffset: 0, vOffset: 224 },
  { id: 99, name: "gia_d", width: 64, height: 32, uOffset: 128, vOffset: 224 },
  { id: 100, name: "gia_d2", width: 64, height: 32, uOffset: 64, vOffset: 224 },
  { id: 101, name: "gia_g", width: 32, height: 32, uOffset: 192, vOffset: 224 },
  { id: 102, name: "gia_g2", width: 32, height: 32, uOffset: 128, vOffset: 224 },
  { id: 103, name: "gmt_eda", width: 32, height: 32, uOffset: 0, vOffset: 352 },
  { id: 104, name: "gonclf", width: 128, height: 64, uOffset: 96, vOffset: 64 },
  { id: 105, name: "gredge", width: 32, height: 32, uOffset: 192, vOffset: 192 },
  { id: 106, name: "hyouga", width: 64, height: 64, uOffset: 192, vOffset: 64 },
  { id: 107, name: "iceclf", width: 64, height: 32, uOffset: 64, vOffset: 96 },
  { id: 108, name: "iceclfd", width: 64, height: 32, uOffset: 128, vOffset: 96 },
  { id: 109, name: "iceclfg", width: 32, height: 32, uOffset: 32, vOffset: 224 },
  { id: 110, name: "jun", width: 64, height: 64, uOffset: 192, vOffset: 192 },
  { id: 111, name: "jun_d", width: 64, height: 64, uOffset: 128, vOffset: 192 },
  { id: 112, name: "jun_e", width: 64, height: 16, uOffset: 0, vOffset: 240 },
  { id: 113, name: "jun_gro", width: 64, height: 64, uOffset: 0, vOffset: 64 },
  { id: 114, name: "junmk", width: 32, height: 32, uOffset: 0, vOffset: 96 },
  { id: 115, name: "junn01", width: 32, height: 32, uOffset: 160, vOffset: 112 },
  { id: 116, name: "junn02", width: 32, height: 32, uOffset: 192, vOffset: 112 },
  { id: 117, name: "junn03", width: 32, height: 32, uOffset: 224, vOffset: 112 },
  { id: 118, name: "junn04", width: 32, height: 32, uOffset: 64, vOffset: 128 },
  { id: 119, name: "jutmpl01", width: 64, height: 64, uOffset: 128, vOffset: 192 },
  { id: 120, name: "lake-e", width: 32, height: 32, uOffset: 96, vOffset: 192 },
  { id: 121, name: "lake_ef", width: 32, height: 32, uOffset: 128, vOffset: 224 },
  { id: 122, name: "lake_fl", width: 128, height: 32, uOffset: 160, vOffset: 224 },
  { id: 123, name: "lostclf", width: 32, height: 64, uOffset: 128, vOffset: 384 },
  { id: 124, name: "lostmt", width: 128, height: 32, uOffset: 128, vOffset: 448 },
  { id: 125, name: "lostmtd", width: 128, height: 32, uOffset: 128, vOffset: 480 },
  { id: 126, name: "lostmts", width: 64, height: 32, uOffset: 160, vOffset: 384 },
  { id: 127, name: "lostwd_e", width: 32, height: 32, uOffset: 64, vOffset: 480 },
  { id: 128, name: "lostwod", width: 64, height: 64, uOffset: 0, vOffset: 448 },
  { id: 129, name: "lst1", width: 32, height: 32, uOffset: 192, vOffset: 256 },
  { id: 130, name: "lstwd_e2", width: 32, height: 32, uOffset: 96, vOffset: 480 },
  { id: 131, name: "mzes", width: 32, height: 32, uOffset: 224, vOffset: 128 },
  { id: 132, name: "mzmt_e", width: 128, height: 64, uOffset: 128, vOffset: 64 },
  { id: 133, name: "mzmt_ed", width: 128, height: 32, uOffset: 128, vOffset: 128 },
  { id: 134, name: "mzmt_edw", width: 128, height: 32, uOffset: 128, vOffset: 160 },
  { id: 135, name: "mzmt_ew", width: 128, height: 32, uOffset: 0, vOffset: 128 },
  { id: 136, name: "mzmt_o", width: 128, height: 32, uOffset: 64, vOffset: 416 },
  { id: 137, name: "mzmt_od", width: 128, height: 32, uOffset: 64, vOffset: 448 },
  { id: 138, name: "mzmt_s", width: 128, height: 32, uOffset: 0, vOffset: 192 },
  { id: 139, name: "mzmt_sd", width: 128, height: 32, uOffset: 0, vOffset: 160 },
  { id: 140, name: "md01", width: 32, height: 32, uOffset: 96, vOffset: 16 },
  { id: 141, name: "md02", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 142, name: "md03", width: 16, height: 16, uOffset: 112, vOffset: 64 },
  { id: 143, name: "md04", width: 32, height: 32, uOffset: 128, vOffset: 16 },
  { id: 144, name: "md05", width: 64, height: 16, uOffset: 96, vOffset: 0 },
  { id: 145, name: "md06", width: 16, height: 32, uOffset: 96, vOffset: 48 },
  { id: 146, name: "md07", width: 16, height: 16, uOffset: 112, vOffset: 48 },
  { id: 147, name: "md_mt", width: 128, height: 128, uOffset: 128, vOffset: 0 },
  { id: 148, name: "md_mtd", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 149, name: "md_mts", width: 64, height: 128, uOffset: 64, vOffset: 160 },
  { id: 150, name: "md_snow", width: 128, height: 32, uOffset: 128, vOffset: 0 },
  { id: 151, name: "md_snw2", width: 128, height: 32, uOffset: 128, vOffset: 32 },
  { id: 152, name: "md_snwd", width: 128, height: 64, uOffset: 0, vOffset: 128 },
  { id: 153, name: "md_snwe", width: 64, height: 64, uOffset: 96, vOffset: 320 },
  { id: 154, name: "md_snws", width: 64, height: 64, uOffset: 128, vOffset: 128 },
  { id: 155, name: "md_snwt", width: 128, height: 32, uOffset: 0, vOffset: 192 },
  { id: 156, name: "md_snww", width: 32, height: 32, uOffset: 224, vOffset: 224 },
  { id: 157, name: "md_sw_s", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 158, name: "md_swd2", width: 32, height: 32, uOffset: 192, vOffset: 256 },
  { id: 159, name: "md_swnp", width: 128, height: 128, uOffset: 0, vOffset: 96 },
  { id: 160, name: "mdsrt_e", width: 128, height: 32, uOffset: 128, vOffset: 192 },
  { id: 161, name: "mdsrt_ed", width: 128, height: 32, uOffset: 128, vOffset: 224 },
  { id: 162, name: "mdsrt_eg", width: 32, height: 32, uOffset: 64, vOffset: 224 },
  { id: 163, name: "midil", width: 32, height: 32, uOffset: 32, vOffset: 192 },
  { id: 164, name: "midild", width: 32, height: 32, uOffset: 224, vOffset: 192 },
  { id: 165, name: "mt_ewg", width: 32, height: 32, uOffset: 64, vOffset: 96 },
  { id: 166, name: "mt_road", width: 64, height: 64, uOffset: 192, vOffset: 128 },
  { id: 167, name: "mt_se", width: 32, height: 32, uOffset: 160, vOffset: 416 },
  { id: 168, name: "mt_se2", width: 64, height: 64, uOffset: 128, vOffset: 256 },
  { id: 169, name: "mt_sg01", width: 32, height: 32, uOffset: 0, vOffset: 224 },
  { id: 170, name: "mt_sg02", width: 32, height: 32, uOffset: 32, vOffset: 224 },
  { id: 171, name: "mt_sg03", width: 32, height: 32, uOffset: 0, vOffset: 192 },
  { id: 172, name: "mt_sg04", width: 32, height: 32, uOffset: 160, vOffset: 384 },
  { id: 173, name: "mtcoin", width: 64, height: 64, uOffset: 0, vOffset: 256 },
  { id: 174, name: "mtwas_e", width: 128, height: 32, uOffset: 0, vOffset: 224 },
  { id: 175, name: "mtwas_ed", width: 128, height: 32, uOffset: 0, vOffset: 224 },
  { id: 176, name: "ncol_gro", width: 64, height: 64, uOffset: 64, vOffset: 384 },
  { id: 177, name: "ncole01", width: 32, height: 32, uOffset: 224, vOffset: 384 },
  { id: 178, name: "ncole02", width: 32, height: 32, uOffset: 192, vOffset: 416 },
  { id: 179, name: "nivl_gro", width: 64, height: 64, uOffset: 128, vOffset: 384 },
  { id: 180, name: "nivl_mt", width: 128, height: 64, uOffset: 0, vOffset: 0 },
  { id: 181, name: "nivl_top", width: 32, height: 32, uOffset: 0, vOffset: 64 },
  { id: 182, name: "nivlr", width: 32, height: 32, uOffset: 192, vOffset: 384 },
  { id: 183, name: "port", width: 32, height: 32, uOffset: 96, vOffset: 224 },
  { id: 184, name: "port_d", width: 32, height: 32, uOffset: 160, vOffset: 0 },
  { id: 185, name: "rzclf02", width: 64, height: 64, uOffset: 128, vOffset: 128 },
  { id: 186, name: "rct_gro", width: 64, height: 128, uOffset: 0, vOffset: 416 },
  { id: 187, name: "riv_cls", width: 64, height: 64, uOffset: 64, vOffset: 0 },
  { id: 188, name: "riv_l1", width: 32, height: 32, uOffset: 96, vOffset: 320 },
  { id: 189, name: "riv_m", width: 32, height: 32, uOffset: 0, vOffset: 224 },
  { id: 190, name: "rivr", width: 32, height: 32, uOffset: 64, vOffset: 224 },
  { id: 191, name: "rivrclf", width: 64, height: 64, uOffset: 128, vOffset: 192 },
  { id: 192, name: "rivs1", width: 32, height: 32, uOffset: 128, vOffset: 320 },
  { id: 193, name: "rivshr", width: 64, height: 64, uOffset: 192, vOffset: 192 },
  { id: 194, name: "rivssr", width: 64, height: 32, uOffset: 192, vOffset: 224 },
  { id: 195, name: "rivstrt", width: 32, height: 32, uOffset: 192, vOffset: 160 },
  { id: 196, name: "rm1", width: 32, height: 32, uOffset: 32, vOffset: 288 },
  { id: 197, name: "rocet", width: 32, height: 32, uOffset: 128, vOffset: 160 },
  { id: 198, name: "rs_ss", width: 32, height: 32, uOffset: 96, vOffset: 224 },
  { id: 199, name: "sango", width: 32, height: 32, uOffset: 224, vOffset: 320 },
  { id: 200, name: "sango2", width: 32, height: 32, uOffset: 224, vOffset: 352 },
  { id: 201, name: "sango3", width: 32, height: 32, uOffset: 128, vOffset: 384 },
  { id: 202, name: "sango4", width: 64, height: 64, uOffset: 0, vOffset: 384 },
  { id: 203, name: "sdun", width: 64, height: 64, uOffset: 0, vOffset: 160 },
  { id: 204, name: "sdun02", width: 64, height: 64, uOffset: 64, vOffset: 160 },
  { id: 205, name: "sh1", width: 32, height: 32, uOffset: 32, vOffset: 256 },
  { id: 206, name: "sh_s1", width: 32, height: 32, uOffset: 224, vOffset: 288 },
  { id: 207, name: "shedge", width: 32, height: 64, uOffset: 160, vOffset: 160 },
  { id: 208, name: "shlm_1", width: 32, height: 32, uOffset: 192, vOffset: 320 },
  { id: 209, name: "shol", width: 128, height: 128, uOffset: 128, vOffset: 96 },
  { id: 210, name: "shol_s", width: 64, height: 64, uOffset: 192, vOffset: 192 },
  { id: 211, name: "shor", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 212, name: "shor_s", width: 64, height: 64, uOffset: 128, vOffset: 192 },
  { id: 213, name: "shor_s2", width: 32, height: 32, uOffset: 224, vOffset: 416 },
  { id: 214, name: "shor_v", width: 32, height: 32, uOffset: 192, vOffset: 0 },
  { id: 215, name: "silo", width: 32, height: 32, uOffset: 224, vOffset: 32 },
  { id: 216, name: "slope", width: 128, height: 32, uOffset: 0, vOffset: 384 },
  { id: 217, name: "snow_es", width: 32, height: 32, uOffset: 192, vOffset: 480 },
  { id: 218, name: "snow_es2", width: 32, height: 32, uOffset: 224, vOffset: 480 },
  { id: 219, name: "snow_es3", width: 32, height: 32, uOffset: 224, vOffset: 448 },
  { id: 220, name: "snw_mt", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 221, name: "snw_mtd", width: 128, height: 128, uOffset: 128, vOffset: 0 },
  { id: 222, name: "snw_mte", width: 64, height: 32, uOffset: 0, vOffset: 96 },
  { id: 223, name: "snw_mted", width: 64, height: 32, uOffset: 64, vOffset: 96 },
  { id: 224, name: "snw_mts", width: 64, height: 128, uOffset: 64, vOffset: 0 },
  { id: 225, name: "snw_mts2", width: 64, height: 32, uOffset: 128, vOffset: 192 },
  { id: 226, name: "snwfld", width: 64, height: 64, uOffset: 0, vOffset: 64 },
  { id: 227, name: "snwfld_s", width: 64, height: 32, uOffset: 128, vOffset: 128 },
  { id: 228, name: "snwgra", width: 64, height: 64, uOffset: 192, vOffset: 192 },
  { id: 229, name: "snwhm01", width: 32, height: 32, uOffset: 32, vOffset: 0 },
  { id: 230, name: "snwhm02", width: 32, height: 32, uOffset: 32, vOffset: 32 },
  { id: 231, name: "snwods", width: 32, height: 32, uOffset: 224, vOffset: 192 },
  { id: 232, name: "snwood", width: 64, height: 64, uOffset: 192, vOffset: 128 },
  { id: 233, name: "snwtrk", width: 32, height: 64, uOffset: 96, vOffset: 256 },
  { id: 234, name: "sse_s1", width: 32, height: 32, uOffset: 32, vOffset: 320 },
  { id: 235, name: "ssee1", width: 32, height: 32, uOffset: 64, vOffset: 288 },
  { id: 236, name: "sst1", width: 32, height: 32, uOffset: 224, vOffset: 256 },
  { id: 237, name: "stown_r", width: 32, height: 32, uOffset: 192, vOffset: 256 },
  { id: 238, name: "stw_gro", width: 64, height: 64, uOffset: 0, vOffset: 384 },
  { id: 239, name: "subrg2", width: 32, height: 32, uOffset: 224, vOffset: 160 },
  { id: 240, name: "susbrg", width: 64, height: 64, uOffset: 192, vOffset: 96 },
  { id: 241, name: "sw_se", width: 64, height: 64, uOffset: 0, vOffset: 0 },
  { id: 242, name: "swclf_l", width: 64, height: 64, uOffset: 64, vOffset: 128 },
  { id: 243, name: "swclf_ld", width: 64, height: 64, uOffset: 192, vOffset: 128 },
  { id: 244, name: "swclf_lg", width: 32, height: 64, uOffset: 0, vOffset: 192 },
  { id: 245, name: "swclf_s", width: 64, height: 32, uOffset: 128, vOffset: 96 },
  { id: 246, name: "swclf_sd", width: 64, height: 32, uOffset: 192, vOffset: 96 },
  { id: 247, name: "swclf_sg", width: 32, height: 32, uOffset: 32, vOffset: 192 },
  { id: 248, name: "swclf_wg", width: 32, height: 32, uOffset: 192, vOffset: 192 },
  { id: 249, name: "swfld_s2", width: 64, height: 32, uOffset: 128, vOffset: 160 },
  { id: 250, name: "swfld_s3", width: 32, height: 32, uOffset: 160, vOffset: 192 },
  { id: 251, name: "swmd_cg", width: 32, height: 32, uOffset: 128, vOffset: 192 },
  { id: 252, name: "swmd_clf", width: 64, height: 32, uOffset: 64, vOffset: 192 },
  { id: 253, name: "swp1", width: 32, height: 32, uOffset: 0, vOffset: 288 },
  { id: 254, name: "trk", width: 64, height: 64, uOffset: 128, vOffset: 0 },
  { id: 255, name: "tyo_f", width: 128, height: 128, uOffset: 128, vOffset: 128 },
  { id: 256, name: "tyosnw", width: 64, height: 128, uOffset: 64, vOffset: 384 },
  { id: 257, name: "uf1", width: 32, height: 32, uOffset: 160, vOffset: 256 },
  { id: 258, name: "utai01", width: 32, height: 32, uOffset: 32, vOffset: 96 },
  { id: 259, name: "utai02", width: 32, height: 32, uOffset: 224, vOffset: 64 },
  { id: 260, name: "utai_gro", width: 64, height: 64, uOffset: 128, vOffset: 96 },
  { id: 261, name: "utaimt", width: 32, height: 32, uOffset: 0, vOffset: 128 },
  { id: 262, name: "utaimtd", width: 32, height: 32, uOffset: 96, vOffset: 96 },
  { id: 263, name: "utaimtg", width: 32, height: 32, uOffset: 96, vOffset: 128 },
  { id: 264, name: "wa1", width: 32, height: 32, uOffset: 192, vOffset: 320 },
  { id: 265, name: "wzs1", width: 32, height: 32, uOffset: 128, vOffset: 288 },
  { id: 266, name: "wzshr", width: 32, height: 32, uOffset: 160, vOffset: 32 },
  { id: 267, name: "wzshr2", width: 32, height: 32, uOffset: 32, vOffset: 128 },
  { id: 268, name: "wzshrs", width: 32, height: 32, uOffset: 32, vOffset: 160 },
  { id: 269, name: "was", width: 128, height: 128, uOffset: 0, vOffset: 96 },
  { id: 270, name: "was_d", width: 64, height: 32, uOffset: 0, vOffset: 224 },
  { id: 271, name: "was_g", width: 64, height: 64, uOffset: 0, vOffset: 192 },
  { id: 272, name: "was_s", width: 128, height: 128, uOffset: 128, vOffset: 0 },
  { id: 273, name: "wasfld", width: 64, height: 64, uOffset: 64, vOffset: 256 },
  { id: 274, name: "wdedge", width: 64, height: 64, uOffset: 64, vOffset: 160 },
  { id: 275, name: "we1", width: 32, height: 32, uOffset: 96, vOffset: 256 },
  { id: 276, name: "we_s1", width: 32, height: 32, uOffset: 160, vOffset: 288 },
  { id: 277, name: "wedged", width: 32, height: 64, uOffset: 128, vOffset: 160 },
  { id: 278, name: "wod-e2", width: 32, height: 32, uOffset: 64, vOffset: 224 },
  { id: 279, name: "wood", width: 64, height: 64, uOffset: 192, vOffset: 0 },
  { id: 280, name: "wood_d", width: 64, height: 64, uOffset: 192, vOffset: 160 },
  { id: 281, name: "wtrk", width: 32, height: 64, uOffset: 64, vOffset: 96 }
];

export const WORLD_MAP_UNDERWATER_TEXTURES: WorldMapTexture[] = [
  { id: 0, name: "cltr", width: 128, height: 128, uOffset: 0, vOffset: 0 },
  { id: 1, name: "lake_a", width: 128, height: 256, uOffset: 0, vOffset: 0 },
  { id: 2, name: "rock", width: 256, height: 256, uOffset: 0, vOffset: 0 },
  { id: 3, name: "scave", width: 256, height: 256, uOffset: 0, vOffset: 0 },
  { id: 4, name: "ssand", width: 256, height: 256, uOffset: 0, vOffset: 0 },
  { id: 5, name: "swall02", width: 256, height: 256, uOffset: 0, vOffset: 0 },
  { id: 6, name: "sng01", width: 128, height: 128, uOffset: 128, vOffset: 0 },
  { id: 7, name: "sng02", width: 128, height: 128, uOffset: 128, vOffset: 0 },

];

export const WORLD_MAP_GLACIER_TEXTURES: WorldMapTexture[] = [
  { id: 0, name: "hokola01", width: 64, height: 64, uOffset: 0, vOffset: 0 },
  { id: 1, name: "hokola02", width: 64, height: 64, uOffset: 0, vOffset: 128 },
  { id: 2, name: "snwfldl", width: 64, height: 64, uOffset: 0, vOffset: 32 },
  { id: 3, name: "snwfld2", width: 64, height: 64, uOffset: 0, vOffset: 192 },
];
