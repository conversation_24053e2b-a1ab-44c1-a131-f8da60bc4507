import * as React from "react";
import { useState } from "react";
import Color from 'color';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  ColorPicker,
  ColorPickerAlpha,
  ColorPickerEyeDropper,
  ColorPickerFormat,
  ColorPickerHue,
  ColorPickerOutput,
  ColorPickerSelection,
} from '@/components/ui/shadcn-io/color-picker';
import { cn } from "@/lib/utils";

interface ColorSquareProps {
  value: [number, number, number];
  onChange: (color: [number, number, number]) => void;
  className?: string;
  disabled?: boolean;
}

export function ColorSquare({ value, onChange, className, disabled = false }: ColorSquareProps) {
  const [isOpen, setIsOpen] = useState(false);

  const rgbToHex = (rgb: [number, number, number]) => {
    return "#" + rgb.map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  };

  const hexToRgb = (hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : [0, 0, 0];
  };

  const handleColorChange = (colorValue: Parameters<typeof Color.rgb>[0]) => {
    if (Array.isArray(colorValue) && colorValue.length >= 3) {
      const rgb: [number, number, number] = [
        Math.round(colorValue[0]),
        Math.round(colorValue[1]),
        Math.round(colorValue[2])
      ];
      onChange(rgb);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "h-6 w-6 p-0 border border-gray-300 rounded",
            disabled && "cursor-not-allowed opacity-50",
            className
          )}
          disabled={disabled}
          style={{ backgroundColor: rgbToHex(value) }}
        />
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3">
        <ColorPicker
          value={rgbToHex(value)}
          onChange={handleColorChange}
          className="w-full"
        >
          <div className="space-y-3">
            <ColorPickerSelection className="h-32" />
            <div className="flex items-center gap-2">
              <ColorPickerEyeDropper />
              <div className="grid w-full gap-1">
                <ColorPickerHue />
                <ColorPickerAlpha />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ColorPickerOutput />
              <ColorPickerFormat />
            </div>
          </div>
        </ColorPicker>
      </PopoverContent>
    </Popover>
  );
}
