{"$message_type":"diagnostic","message":"the trait bound `FieldLights: CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src\\commands.rs","byte_start":7888,"byte_end":7905,"line_start":256,"line_end":256,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"the trait `updater::_::_serde::Deserialize<'_>` is not implemented for `FieldLights`, which is required by `FieldLights: CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\commands.rs","byte_start":8143,"byte_end":9555,"line_start":262,"line_end":309,"column_start":5,"column_end":6,"is_primary":false,"text":[{"text":"    tauri::generate_handler![","highlight_start":5,"highlight_end":30},{"text":"        read_memory_byte,","highlight_start":1,"highlight_end":26},{"text":"        read_memory_short,","highlight_start":1,"highlight_end":27},{"text":"        read_memory_int,","highlight_start":1,"highlight_end":25},{"text":"        read_memory_float,","highlight_start":1,"highlight_end":27},{"text":"        read_memory_buffer,","highlight_start":1,"highlight_end":28},{"text":"        read_memory_signed_short,","highlight_start":1,"highlight_end":34},{"text":"        read_memory_signed_int,","highlight_start":1,"highlight_end":32},{"text":"        write_memory_byte,","highlight_start":1,"highlight_end":27},{"text":"        write_memory_short,","highlight_start":1,"highlight_end":28},{"text":"        write_memory_signed_short,","highlight_start":1,"highlight_end":35},{"text":"        write_memory_int,","highlight_start":1,"highlight_end":26},{"text":"        write_memory_signed_int,","highlight_start":1,"highlight_end":33},{"text":"        write_memory_float,","highlight_start":1,"highlight_end":28},{"text":"        write_memory_buffer,","highlight_start":1,"highlight_end":29},{"text":"        set_memory_protection,","highlight_start":1,"highlight_end":31},{"text":"        read_ff7_data,","highlight_start":1,"highlight_end":23},{"text":"        get_ff7_addresses,","highlight_start":1,"highlight_end":27},{"text":"        read_enemy_data,","highlight_start":1,"highlight_end":25},{"text":"        get_chocobo_rating_for_scene,","highlight_start":1,"highlight_end":38},{"text":"        read_item_names,","highlight_start":1,"highlight_end":25},{"text":"        read_materia_names,","highlight_start":1,"highlight_end":28},{"text":"        read_key_item_names,","highlight_start":1,"highlight_end":29},{"text":"        read_command_names,","highlight_start":1,"highlight_end":28},{"text":"        read_attack_names,","highlight_start":1,"highlight_end":27},{"text":"        read_enemy_attack_names,","highlight_start":1,"highlight_end":33},{"text":"        read_item_data,","highlight_start":1,"highlight_end":24},{"text":"        read_world_field_tbl_data,","highlight_start":1,"highlight_end":35},{"text":"        read_variables_bank,","highlight_start":1,"highlight_end":29},{"text":"        write_variable_8bit,","highlight_start":1,"highlight_end":29},{"text":"        write_variable_16bit,","highlight_start":1,"highlight_end":30},{"text":"        get_current_game_directory,","highlight_start":1,"highlight_end":36},{"text":"        show_map_window,","highlight_start":1,"highlight_end":25},{"text":"        log_from_frontend,","highlight_start":1,"highlight_end":27},{"text":"        check_for_updates,","highlight_start":1,"highlight_end":27},{"text":"        execute_update,","highlight_start":1,"highlight_end":24},{"text":"        read_battle_scenes,","highlight_start":1,"highlight_end":28},{"text":"        read_chocobo_data,","highlight_start":1,"highlight_end":27},{"text":"        write_chocobo_slot,","highlight_start":1,"highlight_end":28},{"text":"        write_fenced_chocobo,","highlight_start":1,"highlight_end":30},{"text":"        write_stable_occupation_mask,","highlight_start":1,"highlight_end":38},{"text":"        write_chocobo_name,","highlight_start":1,"highlight_end":28},{"text":"        write_chocobo_stamina,","highlight_start":1,"highlight_end":31},{"text":"        write_stables_owned,","highlight_start":1,"highlight_end":29},{"text":"        write_occupied_stables,","highlight_start":1,"highlight_end":32},{"text":"        set_chocobo_can_mate,","highlight_start":1,"highlight_end":30},{"text":"        write_field_lights,","highlight_start":1,"highlight_end":28},{"text":"    ]","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\commands.rs","byte_start":8143,"byte_end":9555,"line_start":262,"line_end":309,"column_start":5,"column_end":6,"is_primary":false,"text":[{"text":"    tauri::generate_handler![","highlight_start":5,"highlight_end":30},{"text":"        read_memory_byte,","highlight_start":1,"highlight_end":26},{"text":"        read_memory_short,","highlight_start":1,"highlight_end":27},{"text":"        read_memory_int,","highlight_start":1,"highlight_end":25},{"text":"        read_memory_float,","highlight_start":1,"highlight_end":27},{"text":"        read_memory_buffer,","highlight_start":1,"highlight_end":28},{"text":"        read_memory_signed_short,","highlight_start":1,"highlight_end":34},{"text":"        read_memory_signed_int,","highlight_start":1,"highlight_end":32},{"text":"        write_memory_byte,","highlight_start":1,"highlight_end":27},{"text":"        write_memory_short,","highlight_start":1,"highlight_end":28},{"text":"        write_memory_signed_short,","highlight_start":1,"highlight_end":35},{"text":"        write_memory_int,","highlight_start":1,"highlight_end":26},{"text":"        write_memory_signed_int,","highlight_start":1,"highlight_end":33},{"text":"        write_memory_float,","highlight_start":1,"highlight_end":28},{"text":"        write_memory_buffer,","highlight_start":1,"highlight_end":29},{"text":"        set_memory_protection,","highlight_start":1,"highlight_end":31},{"text":"        read_ff7_data,","highlight_start":1,"highlight_end":23},{"text":"        get_ff7_addresses,","highlight_start":1,"highlight_end":27},{"text":"        read_enemy_data,","highlight_start":1,"highlight_end":25},{"text":"        get_chocobo_rating_for_scene,","highlight_start":1,"highlight_end":38},{"text":"        read_item_names,","highlight_start":1,"highlight_end":25},{"text":"        read_materia_names,","highlight_start":1,"highlight_end":28},{"text":"        read_key_item_names,","highlight_start":1,"highlight_end":29},{"text":"        read_command_names,","highlight_start":1,"highlight_end":28},{"text":"        read_attack_names,","highlight_start":1,"highlight_end":27},{"text":"        read_enemy_attack_names,","highlight_start":1,"highlight_end":33},{"text":"        read_item_data,","highlight_start":1,"highlight_end":24},{"text":"        read_world_field_tbl_data,","highlight_start":1,"highlight_end":35},{"text":"        read_variables_bank,","highlight_start":1,"highlight_end":29},{"text":"        write_variable_8bit,","highlight_start":1,"highlight_end":29},{"text":"        write_variable_16bit,","highlight_start":1,"highlight_end":30},{"text":"        get_current_game_directory,","highlight_start":1,"highlight_end":36},{"text":"        show_map_window,","highlight_start":1,"highlight_end":25},{"text":"        log_from_frontend,","highlight_start":1,"highlight_end":27},{"text":"        check_for_updates,","highlight_start":1,"highlight_end":27},{"text":"        execute_update,","highlight_start":1,"highlight_end":24},{"text":"        read_battle_scenes,","highlight_start":1,"highlight_end":28},{"text":"        read_chocobo_data,","highlight_start":1,"highlight_end":27},{"text":"        write_chocobo_slot,","highlight_start":1,"highlight_end":28},{"text":"        write_fenced_chocobo,","highlight_start":1,"highlight_end":30},{"text":"        write_stable_occupation_mask,","highlight_start":1,"highlight_end":38},{"text":"        write_chocobo_name,","highlight_start":1,"highlight_end":28},{"text":"        write_chocobo_stamina,","highlight_start":1,"highlight_end":31},{"text":"        write_stables_owned,","highlight_start":1,"highlight_end":29},{"text":"        write_occupied_stables,","highlight_start":1,"highlight_end":32},{"text":"        set_chocobo_can_mate,","highlight_start":1,"highlight_end":30},{"text":"        write_field_lights,","highlight_start":1,"highlight_end":28},{"text":"    ]","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tauri::generate_handler!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\tauri-macros-2.0.3\\src/lib.rs","byte_start":2060,"byte_end":2117,"line_start":68,"line_end":68,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn generate_handler(item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"__cmd__write_field_lights!","def_site_span":{"file_name":"src\\commands.rs","byte_start":7888,"byte_end":7905,"line_start":256,"line_end":256,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `CommandArg<'de, R>`:\n  <AppHandle<R> as CommandArg<'de, R>>\n  <Channel<TSend> as CommandArg<'de, R>>\n  <CommandScope<T> as CommandArg<'a, R>>\n  <GlobalScope<T> as CommandArg<'a, R>>\n  <tauri::State<'r, T> as CommandArg<'de, R>>\n  <tauri::Webview<R> as CommandArg<'de, R>>\n  <tauri::WebviewWindow<R> as CommandArg<'de, R>>\n  <tauri::Window<R> as CommandArg<'de, R>>\n  <tauri::ipc::Request<'a> as CommandArg<'a, R>>","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `FieldLights` to implement `CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `FieldLights: CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands.rs:256:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `updater::_::_serde::Deserialize<'_>` is not implemented for `FieldLights`, which is required by `FieldLights: CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tauri::generate_handler![\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        read_memory_byte,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m264\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        read_memory_short,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        read_memory_int,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m308\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        write_field_lights,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m309\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_____-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `CommandArg<'de, R>`:\u001b[0m\n\u001b[0m              <AppHandle<R> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <Channel<TSend> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <CommandScope<T> as CommandArg<'a, R>>\u001b[0m\n\u001b[0m              <GlobalScope<T> as CommandArg<'a, R>>\u001b[0m\n\u001b[0m              <tauri::State<'r, T> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <tauri::Webview<R> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <tauri::WebviewWindow<R> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <tauri::Window<R> as CommandArg<'de, R>>\u001b[0m\n\u001b[0m              <tauri::ipc::Request<'a> as CommandArg<'a, R>>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `FieldLights` to implement `CommandArg<'_, tauri_runtime_wry::Wry<EventLoopMessage>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `__cmd__write_field_lights` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0277`.\u001b[0m\n"}
