{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main", "mapviewer"], "permissions": ["core:path:default", "core:event:default", "core:window:default", "core:app:default", "core:image:default", "core:resources:default", "core:menu:default", "core:tray:default", "shell:allow-open", "updater:default", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "global-shortcut:allow-unregister-all", "window-state:default", "store:default", "dialog:default", "fs:default", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-exists", {"identifier": "fs:scope", "allow": ["**", "**/*", "/**/*"]}, "opener:default", "opener:allow-reveal-item-in-dir", "core:window:allow-set-focus", "core:window:allow-show", "core:window:allow-close", "core:window:allow-create", "core:window:allow-destroy", "core:webview:allow-create-webview", "core:webview:allow-webview-show", "core:webview:allow-set-webview-focus", "core:webview:allow-set-webview-size", "core:window:allow-set-size", "dialog:allow-message", "core:window:allow-set-always-on-top"]}