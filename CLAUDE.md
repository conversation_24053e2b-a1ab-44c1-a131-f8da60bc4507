# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo containing three Final Fantasy VII-related projects:

1. **ff7-lib** - Rust library for FF7 memory manipulation and data structures
2. **landscaper** - FF7 world map editor (Tauri desktop app)
3. **ultima** - Real-time FF7 game editor (Tauri desktop app)

All projects work with the English Steam version of Final Fantasy VII PC and share common file format parsing capabilities.

## Commands

### Global Commands
```bash
# Navigate to specific project
cd landscaper/    # FF7 world map editor
cd ultima/        # Real-time game editor
cd ff7-lib/       # Shared Rust library
```

### Landscaper (World Map Editor)
```bash
cd landscaper/
npm install                 # Install dependencies
npm run tauri dev          # Run development server
npm run tauri build        # Build desktop application
npm test                   # Run Vitest unit tests
npm run extract-scripts    # Extract world scripts from game files

# UI Components
npx shadcn@latest add <component>  # Add shadcn-ui components
```

### <PERSON><PERSON><PERSON> (Real-time Game Editor)
```bash
cd ultima/
npm install           # Install dependencies
npm run tauri dev     # Run development server
npm run tauri build   # Build desktop application

# Windows PowerShell build script
./build.ps1          # Run complete build with signing
```

### ff7-lib (Rust Library)
```bash
cd ff7-lib/
cargo build          # Build library
cargo test           # Run tests
cargo doc --open     # Generate and open documentation
```

## Architecture

### Shared ff7-lib Library
- **Location**: `ff7-lib/`
- **Purpose**: Core Rust library providing FF7 memory manipulation, data structures, and file format parsers
- **Key modules**: 
  - `addresses.rs` - Memory address definitions
  - `data/` - Game data reading functions
  - `types/` - Shared data structures
  - `ff7text.rs` - FF7 text encoding/decoding

### Landscaper Architecture
- **Tech Stack**: React 18 + TypeScript + Vite + TailwindCSS + shadcn-ui + Tauri 2.0
- **State Management**: Jotai atomic state management
- **3D Rendering**: Three.js with React Three Fiber
- **Code Editing**: Ace Editor for custom worldscript language

**Key Features**:
- 3D world map viewer with multiple rendering modes (textured, terrain, region, scripts)
- Custom worldscript language parser and editor with syntax highlighting
- Direct editing of FF7 binary files (MAP, MES, TEX, LGP formats)
- Triangle-based mesh editing with visual overlays
- Message/dialogue editing from LGP archives

**Core Directories**:
- `src/ff7/` - FF7 file format parsers (shared with ultima)
- `src/components/tabs/` - Main app features (Messages, Map, Textures, Scripts, Encounters)
- `src/components/map/` - 3D map viewer and editing tools
- `src/components/script/` - Worldscript editor with custom language support
- `src/hooks/` - Jotai-based state management

### Ultima Architecture
- **Tech Stack**: React + TypeScript + Vite + TailwindCSS + shadcn-ui + Tauri
- **State Management**: React Context + Jotai
- **3D Rendering**: Three.js with React Three Fiber for world map
- **Memory Interface**: Custom Rust backend for real-time game memory manipulation

**Key Features**:
- Real-time memory reading/writing to running FF7 process
- Live game state editing (stats, inventory, variables)
- Interactive 3D world map with teleportation
- Save state management
- Battle editing and cheats
- Global hotkey support

**Core Structure**:
- `src/modules/` - Main UI modules (General, Battle, Field, Party, World, Advanced)
- `src/FF7Context.tsx` - Central state provider
- `src/useFF7.ts` - Core game editing functionality
- `src/memory.ts` - Memory operation interface
- `src-tauri/src/commands.rs` - Rust memory manipulation commands

## File Format Support

Both applications work with FF7 binary files using shared TypeScript parsers in `src/ff7/`:

- **World Maps**: `wmX.MAP` files (geometry, textures, scripts)
- **Messages**: `mes` files from LGP archives  
- **Textures**: Various FF7 texture formats
- **Scripts**: Custom worldscript bytecode with full language implementation
- **Archives**: LGP file format support
- **Encounters**: Battle encounter data

## Development Notes

### Dependencies
- Both Tauri apps depend on the `ff7-lib` Rust crate as a path dependency
- The library must be available at `../../ff7-lib` relative to each project
- All projects use the same FF7 file format parsers in `src/ff7/`

### Testing
- Landscaper: Uses Vitest for unit tests (`npm test`)
- Test files: `src/**/*.test.ts`
- After implementing changes, do NOT run dev servers - hot reload is typically already active

### UI Guidelines
- Dark theme with "slate" base color scheme
- Use existing shadcn-ui components when possible
- Consistent component patterns across both apps

### State Management Patterns
- **Landscaper**: Jotai atoms with domain-specific hooks (`useMapState`, `useScriptState`, etc.)
- **Ultima**: React Context for global state + Jotai for specific features

### Memory Address Management
- FF7 memory addresses defined in `ff7Addresses.ts` (ultima) and `addresses.rs` (ff7-lib)
- Coordinate systems use FF7's native units with scaling factors
- Memory operations require game process connection

## Project-Specific Notes

### Landscaper
- Map coordinate system: FF7 coordinates scaled by 0.05, 8192 units per mesh section
- Supports three map types: Overworld (9×7), Underwater (3×4), Great Glacier (2×2)
- Custom worldscript language with full parser implementation

### Ultima  
- Supports English Steam version of FF7 only
- Real-time memory manipulation requires administrator privileges
- Auto-applies configured hacks on game startup
- Save states work independently of FF7's native save system