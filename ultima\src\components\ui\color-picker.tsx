import * as React from "react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface ColorPickerProps {
  value: [number, number, number];
  onChange: (color: [number, number, number]) => void;
  className?: string;
  disabled?: boolean;
}

export function ColorPicker({ value, onChange, className, disabled = false }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempColor, setTempColor] = useState(value);

  const rgbToHex = (rgb: [number, number, number]) => {
    return "#" + rgb.map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  };

  const hexToRgb = (hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : [0, 0, 0];
  };

  const handleColorChange = (newColor: [number, number, number]) => {
    setTempColor(newColor);
    onChange(newColor);
  };

  const handleHexChange = (hex: string) => {
    const rgb = hexToRgb(hex);
    handleColorChange(rgb);
  };

  const handleRgbChange = (index: number, value: string) => {
    const numValue = Math.max(0, Math.min(255, parseInt(value) || 0));
    const newColor: [number, number, number] = [...tempColor];
    newColor[index] = numValue;
    handleColorChange(newColor);
  };

  React.useEffect(() => {
    setTempColor(value);
  }, [value]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            <div
              className="h-4 w-4 rounded border border-gray-300"
              style={{ backgroundColor: rgbToHex(value) }}
            />
            <span className="text-xs">
              RGB({value[0]}, {value[1]}, {value[2]})
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <div
              className="h-8 w-8 rounded border border-gray-300"
              style={{ backgroundColor: rgbToHex(tempColor) }}
            />
            <Input
              type="color"
              value={rgbToHex(tempColor)}
              onChange={(e) => handleHexChange(e.target.value)}
              className="w-16 h-8 p-0 border-0"
            />
            <Input
              type="text"
              value={rgbToHex(tempColor)}
              onChange={(e) => handleHexChange(e.target.value)}
              className="flex-1 text-xs"
              placeholder="#000000"
            />
          </div>
          
          <div className="space-y-2">
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div>
                <label className="block text-xs font-medium mb-1">R</label>
                <Input
                  type="number"
                  min="0"
                  max="255"
                  value={tempColor[0]}
                  onChange={(e) => handleRgbChange(0, e.target.value)}
                  className="text-xs"
                />
              </div>
              <div>
                <label className="block text-xs font-medium mb-1">G</label>
                <Input
                  type="number"
                  min="0"
                  max="255"
                  value={tempColor[1]}
                  onChange={(e) => handleRgbChange(1, e.target.value)}
                  className="text-xs"
                />
              </div>
              <div>
                <label className="block text-xs font-medium mb-1">B</label>
                <Input
                  type="number"
                  min="0"
                  max="255"
                  value={tempColor[2]}
                  onChange={(e) => handleRgbChange(2, e.target.value)}
                  className="text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
